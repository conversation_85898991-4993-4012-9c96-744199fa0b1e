// pages/teacher/[slug]/students.tsx
import { use } from 'blade/server/hooks';
import TeacherStudentsPageWithData from '../../../components/teacher/TeacherStudentsPageWithData.client';

const TeachersStudentsPage = () => {
  // Fetch only teacher users with only the fields we need (much smaller dataset)
  const teacherUsers = use.users({
    with: { role: 'teacher' },
    selecting: ['id', 'name', 'email', 'slug', 'role', 'isVerified', 'createdAt']
  }) || [];

  // Fetch only student users with only the fields we need (smaller than all users)
  const studentUsers = use.users({
    with: { role: 'student' },
    selecting: ['id', 'name', 'email', 'slug', 'role', 'teacherId', 'isActive', 'classId', 'grade', 'username', 'createdAt']
  }) || [];

  // Try to fetch grade levels with only needed fields
  let allGradeLevels: any[] = [];
  try {
    allGradeLevels = use.gradeLevels({
      selecting: ['id', 'name', 'teacherId', 'isActive', 'createdAt']
    }) || [];
  } catch (error) {
    // Model not yet migrated, silently use empty array
    allGradeLevels = [];
  }

  // Try to fetch classes with only needed fields
  let allClasses: any[] = [];
  try {
    allClasses = use.classes({
      selecting: ['id', 'name', 'teacherId', 'isActive', 'gradeLevel', 'createdAt']
    }) || [];
  } catch (error) {
    // Model not yet migrated, silently use empty array
    allClasses = [];
  }

  // Fetch student-teacher relationships with only needed fields
  let allStudentTeachers: any[] = [];
  try {
    allStudentTeachers = use.studentTeachers({
      selecting: ['id', 'studentId', 'teacherId', 'status', 'createdAt']
    }) || [];
  } catch (error) {
    // Model not yet migrated, silently use empty array
    allStudentTeachers = [];
  }

  // Combine teacher and student users for backward compatibility
  const allUsers = [...teacherUsers, ...studentUsers];

  return (
    <TeacherStudentsPageWithData
      allUsers={allUsers}
      allGradeLevels={allGradeLevels}
      allClasses={allClasses}
      allStudentTeachers={allStudentTeachers}
    />
  );
};

// Export as default for Blade framework
export default TeachersStudentsPage;
