// components/teacher/TeacherStudentsPageWithData.client.tsx
'use client';

import { useMemo } from 'react';
import { useParams } from 'blade/hooks';
import { TeacherAuthGuard } from '../auth/AuthGuard.client';
import StudentManagementTabsWithData from '../auth/teacher/students/student-management-tabs-with-data.client';
import NoiseText from '../home/<USER>';

interface User {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
  teacherId?: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  createdAt?: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  sortOrder?: number;
  isActive?: boolean;
}

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface TeacherStudentsPageWithDataProps {
  allUsers: User[];
  allGradeLevels: GradeLevel[];
  allClasses: ClassItem[];
  allStudentTeachers: StudentTeacher[];
}

interface StudentTeacher {
  id: string;
  studentId: string;
  teacherId: string;
  assignedAt: string;
  status: string;
}

const TeacherStudentsPageWithData = ({ allUsers, allGradeLevels, allClasses, allStudentTeachers }: TeacherStudentsPageWithDataProps) => {
  const { slug } = useParams();



  // Find the teacher by slug
  const teacher = useMemo(() =>
    allUsers.find(u => u.slug === slug && u.role === 'teacher'),
    [allUsers, slug]
  );

  // If teacher not found, return null - AuthGuard will handle instant redirect
  if (!teacher) {
    return null; // Blade's instant redirect will handle this
  }

  // Get ALL students for this teacher using StudentTeacher relationships
  // Memoized to prevent unnecessary re-computation
  const { activeStudents, inactiveStudents } = useMemo(() => {
    // Get active and inactive student-teacher relationships for this teacher
    const activeRelationships = allStudentTeachers.filter(st =>
      st.teacherId === teacher.id && st.status === 'active'
    );
    const inactiveRelationships = allStudentTeachers.filter(st =>
      st.teacherId === teacher.id && st.status === 'inactive'
    );

    // Get student user data for active relationships
    const active = activeRelationships
      .map(st => allUsers.find(u => u.id === st.studentId && u.role === 'student'))
      .filter((user): user is User => user !== undefined) // Type-safe filter
      .sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    // Get student user data for inactive relationships
    const inactive = inactiveRelationships
      .map(st => allUsers.find(u => u.id === st.studentId && u.role === 'student'))
      .filter((user): user is User => user !== undefined) // Type-safe filter
      .sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    return {
      activeStudents: active,
      inactiveStudents: inactive
    };
  }, [allUsers, allStudentTeachers, teacher.id]);

  // Filter grade levels for this teacher
  const teacherGradeLevels = useMemo(() => {
    return allGradeLevels.filter(grade => grade.teacherId === teacher.id && grade.isActive !== false);
  }, [allGradeLevels, teacher.id]);

  // Filter classes for this teacher
  const teacherClasses = useMemo(() => {
    return allClasses.filter(classItem => classItem.teacherId === teacher.id && classItem.isActive !== false);
  }, [allClasses, teacher.id]);

  // PERFORMANCE FIX: Memoize the StudentManagementTabsWithData component to prevent re-render loops
  const StudentManagementComponent = useMemo(() => (
    <StudentManagementTabsWithData
      students={activeStudents}
      removedStudents={inactiveStudents}
      teacher={teacher}
      availableGradeLevels={teacherGradeLevels}
      teacherClasses={teacherClasses}
    />
  ), [activeStudents, inactiveStudents, teacher, teacherGradeLevels, teacherClasses]);

  return (
    <TeacherAuthGuard>
      <div className="p-2">
        <NoiseText
              text={` Manage your students and their class assignments.`}
              className="font-redaction-normal text-xl md:text-2xl lg:text-2xl mb-2 leading-tight"
            />

        {/* Use the memoized student management component */}
        {StudentManagementComponent}

      </div>
    </TeacherAuthGuard>
  );
};

export default TeacherStudentsPageWithData;
